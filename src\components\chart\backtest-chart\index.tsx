import dayjs from "dayjs";
import {
	CrosshairMode,
	type IChartApi,
	type TickMarkType,
	type Time,
	TimeFormatterFn,
} from "lightweight-charts";
import {
	CandlestickSeries,
	Chart,
	LineSeries,
	Pane,
	TimeScale,
	TimeScaleFitContentTrigger,
} from "lightweight-charts-react-components";
import React, { useCallback, useEffect, useRef } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { get_play_index } from "@/service/strategy-control/backtest-strategy-control";
import type { BacktestChartConfig } from "@/types/chart/backtest-chart";
import { useBacktestChartStore } from "./backtest-chart-store";
import { KlineLegend, useLegend } from "./legend";
import { SeriesType } from "@/types/chart";

interface BacktestChartProps {
	strategyId: number;
	chartConfig: BacktestChartConfig;
}

const BacktestChart = ({ strategyId, chartConfig }: BacktestChartProps) => {
	console.log("图表配置", chartConfig);

	const {
		chartData: klineData,
		initKlineData,
		stopSimulation,
		setSeriesRef,
		setChartRef,
		setKlineKeyStr,
		setEnabled,
		initObserverSubscriptions,
		cleanupSubscriptions,
	} = useBacktestChartStore();

	// 添加容器引用用于调试
	const containerRef = useRef<HTMLDivElement>(null);
	const chartContainerRef = useRef<HTMLDivElement>(null);

	const { ref, legendData, onCrosshairMove } = useLegend({ data: klineData });

	const playIndex = useRef(0);

	const getPlayIndex = useCallback(() => {
		get_play_index(strategyId).then((index) => {
			playIndex.current = index;
			initKlineData(playIndex.current);
		});
	}, [strategyId, initKlineData]);

	// 初始化配置
	useEffect(() => {
		const klineKeyStr = chartConfig.klineChartConfig.klineKeyStr;
		const enabled = true; // 默认启用 Observer 数据流
		console.log("初始化 BacktestChart 配置:", { klineKeyStr, enabled });
		setKlineKeyStr(klineKeyStr);
		setEnabled(enabled);
		getPlayIndex();
	}, [
		chartConfig.klineChartConfig.klineKeyStr,
		setKlineKeyStr,
		setEnabled,
		getPlayIndex,
	]);

	// 设置series引用到store中，这样store就可以直接使用series.update方法
	useEffect(() => {
		const checkAndSetSeries = () => {
			if (ref.current) {
				const seriesApi = ref.current.api();
				if (seriesApi) {
					console.log("设置series引用到store:", seriesApi);
					setSeriesRef(ref.current);
					return true;
				} else {
					console.warn("series API尚未可用，稍后重试");
					return false;
				}
			}
			return false;
		};

		// 立即检查
		if (!checkAndSetSeries()) {
			// 如果立即检查失败，延迟重试
			const timer = setTimeout(() => {
				checkAndSetSeries();
			}, 100);

			return () => clearTimeout(timer);
		}
	}, [ref, setSeriesRef]);

	// 添加容器尺寸监控
	useEffect(() => {
		const logContainerSizes = () => {
			if (containerRef.current) {
				const containerRect = containerRef.current.getBoundingClientRect();
				console.log("🔍 外层容器尺寸:", {
					width: containerRect.width,
					height: containerRect.height,
					offsetWidth: containerRef.current.offsetWidth,
					offsetHeight: containerRef.current.offsetHeight,
					scrollWidth: containerRef.current.scrollWidth,
					scrollHeight: containerRef.current.scrollHeight,
				});
			}

			if (chartContainerRef.current) {
				const chartRect = chartContainerRef.current.getBoundingClientRect();
				console.log("📊 图表容器尺寸:", {
					width: chartRect.width,
					height: chartRect.height,
					offsetWidth: chartContainerRef.current.offsetWidth,
					offsetHeight: chartContainerRef.current.offsetHeight,
					scrollWidth: chartContainerRef.current.scrollWidth,
					scrollHeight: chartContainerRef.current.scrollHeight,
				});
			}
		};

		// 初始检查
		logContainerSizes();

		// 监听窗口大小变化
		const handleResize = () => {
			console.log("🔄 窗口大小变化，重新检查容器尺寸");
			logContainerSizes();
		};

		window.addEventListener('resize', handleResize);

		// 使用 ResizeObserver 监听容器大小变化
		let resizeObserver: ResizeObserver | null = null;
		if (containerRef.current) {
			resizeObserver = new ResizeObserver((entries) => {
				for (const entry of entries) {
					console.log("📏 容器大小变化:", {
						width: entry.contentRect.width,
						height: entry.contentRect.height,
						target: entry.target.className,
					});
				}
			});
			resizeObserver.observe(containerRef.current);
		}

		return () => {
			window.removeEventListener('resize', handleResize);
			if (resizeObserver) {
				resizeObserver.disconnect();
			}
		};
	}, []);

	// Chart onInit 回调 - 初始化 observer 订阅
	const handleChartInit = (chart: IChartApi) => {
		console.log("Chart 初始化完成:", chart);
		setChartRef(chart);

		// 延迟初始化 observer 订阅，确保所有引用都已设置
		setTimeout(() => {
			initObserverSubscriptions();
		}, 100);

		// 检查图表初始化时的容器尺寸
		setTimeout(() => {
			if (containerRef.current && chartContainerRef.current) {
				console.log("🎯 图表初始化后的容器状态:");
				console.log("外层容器:", containerRef.current.getBoundingClientRect());
				console.log("图表容器:", chartContainerRef.current.getBoundingClientRect());
			}
		}, 200);
	};

	// 组件卸载时清理
	useEffect(() => {
		return () => {
			console.log("BacktestChart 组件卸载，清理资源");
			cleanupSubscriptions();
			stopSimulation();
		};
	}, [cleanupSubscriptions, stopSimulation]);

	const chartOptions = {
		autoSize: true,
		// width: 400,
		// height: 600,
		grid: {
			vertLines: {
				visible: false,
			},
			horzLines: {
				visible: false,
			},
		},
		crosshair: {
			mode: CrosshairMode.Normal,
			vertLine: {
				style: 3,
				color: "#080F25",
			},
			horzLine: {
				style: 3,
				color: "#080F25",
			},
		},
		layout: {
			panes: {
				separatorColor: "#080F25",
			},
		},
		localization: {
			timeFormatter: (time: Time) => {
				// 将时间戳转换为 yyyy-mm-dd hh:mm 格式
				if (typeof time === "number") {
					return dayjs(time * 1000).format("YYYY-MM-DD HH:mm");
				}

				if (typeof time === "object" && time !== null && "year" in time) {
					const date = new Date(time.year, time.month - 1, time.day);
					return dayjs(date).format("YYYY-MM-DD");
				}

				if (typeof time === "string") {
					return dayjs(time).format("YYYY-MM-DD");
				}

				return String(time);
			},
		},
		timeScale: {
			visible: true,
			timeVisible: true,
		},
	};

	return (
		<div ref={containerRef} className="w-full h-full" style={{ border: '2px solid red' }}>
			{/* 图表容器 */}
			<div ref={chartContainerRef} className="relative w-full h-full" style={{ border: '2px solid blue' }}>
				<Chart
					options={chartOptions}
					onCrosshairMove={onCrosshairMove}
					onInit={handleChartInit}
				>
					<Pane>
						<CandlestickSeries ref={ref} data={klineData} reactive={true} />
						{/* 图例 */}
						<KlineLegend klineSeriesData={legendData} />
						{/* 添加主图指标 */}
						{Object.entries(chartConfig.klineChartConfig.indicatorChartConfig).map(([_, indicatorConfig]) => {
							// 主图指标
							if (indicatorConfig.isInMainChart) {
								return indicatorConfig.seriesConfigs.map((seriesConfig) => {
									if (seriesConfig.type === SeriesType.LINE) {
										return (
												<LineSeries key={seriesConfig.name} data={[]} />
											);
										}
								});
							}
						})}
					</Pane>
				</Chart>
			</div>
		</div>
	);
};

export default BacktestChart;
