{"name": "star_river", "private": true, "version": "0.0.0", "author": "<PERSON><PERSON><PERSON>", "description": "Star River App", "type": "module", "main": "electron/main.cjs", "scripts": {"dev": "concurrently \"vite\" \"cross-env NODE_ENV=development electron .\"", "start": "vite", "lint": "eslint .", "preview": "vite preview", "build": "electron-builder", "gen-i18n": "node src/i18n/gen-i18n.cjs", "check": "npx biome check --write"}, "build": {"appId": "com.jabori.starriver", "win": {"target": [{"target": "nsis", "arch": ["x64"]}]}, "nsis": {"oneClick": false, "perMachine": true, "allowToChangeInstallationDirectory": true}}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^5.0.1", "@internationalized/date": "^3.8.0", "@radix-ui/react-accordion": "^1.2.8", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-popover": "^1.1.11", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.4", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/postcss": "^4.0.9", "@tailwindcss/vite": "^4.0.9", "@tanstack/react-table": "^8.21.3", "@xyflow/react": "^12.4.4", "axios": "^1.8.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "electron-squirrel-startup": "^1.0.1", "highcharts": "^12.1.2", "highcharts-react-official": "^3.2.1", "i18next": "^25.2.1", "lightweight-charts": "^5.0.8", "lightweight-charts-react-components": "^1.2.0", "lucide": "^0.525.0", "lucide-react": "^0.476.0", "motion": "^12.4.7", "next-themes": "^0.4.4", "react": "^19.0.0", "react-aria-components": "^1.8.0", "react-day-picker": "^9.7.0", "react-dom": "^19.0.0", "react-hook-form": "^7.55.0", "react-i18next": "^15.5.2", "react-resizable-panels": "^3.0.3", "react-router": "^7.2.0", "react-sortablejs": "^6.1.4", "rxjs": "^7.8.2", "scichart": "^3.5.760", "scichart-react": "^0.1.9", "sonner": "^2.0.1", "sortablejs": "^1.15.6", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "vaul": "^1.1.2", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@biomejs/biome": "2.1.2", "@eslint/js": "^9.19.0", "@types/node": "^22.13.5", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@types/sortablejs": "^1.15.8", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "concurrently": "^9.1.2", "cross-env": "^7.0.3", "electron": "^34.2.0", "electron-builder": "^25.1.8", "eslint": "^9.19.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "postcss": "^8.5.3", "tailwindcss": "^4.0.9", "typescript": "~5.7.2", "typescript-eslint": "^8.22.0", "vite": "^6.1.0", "vite-plugin-static-copy": "^3.0.0"}}